<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>监督App UI设计稿</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            color: white;
            margin-bottom: 30px;
            font-size: 2.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .ui-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .phone-mockup {
            width: 320px;
            height: 640px;
            background: #000;
            border-radius: 30px;
            padding: 20px 15px;
            position: relative;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            margin: 0 auto;
        }

        .screen {
            width: 100%;
            height: 100%;
            background: white;
            border-radius: 20px;
            overflow: hidden;
            position: relative;
        }

        .status-bar {
            height: 30px;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 15px;
            color: white;
            font-size: 12px;
            font-weight: 500;
        }

        .page-title {
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            background: white;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .content {
            padding: 20px;
            height: calc(100% - 30px);
            overflow-y: auto;
        }

        /* 登录页面样式 */
        .login-form {
            padding: 40px 20px;
            text-align: center;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }

        .input-group {
            margin-bottom: 20px;
        }

        .input-field {
            width: 100%;
            padding: 15px;
            border: 2px solid #eee;
            border-radius: 12px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .input-field:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn-primary {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
        }

        /* 角色选择页面 */
        .role-selection {
            padding: 40px 20px;
            text-align: center;
        }

        .role-card {
            background: white;
            border-radius: 16px;
            padding: 30px 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border: 2px solid transparent;
            cursor: pointer;
            transition: all 0.3s;
        }

        .role-card:hover {
            border-color: #667eea;
            transform: translateY(-5px);
        }

        .role-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }

        /* 主页样式 */
        .dashboard {
            padding: 20px;
        }

        .welcome-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 16px;
            margin-bottom: 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }

        /* 任务卡片 */
        .task-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }

        .task-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: #333;
        }

        .task-time {
            font-size: 12px;
            color: #666;
        }

        .task-status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            margin-top: 10px;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-approved {
            background: #d4edda;
            color: #155724;
        }

        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }

        /* 底部导航 */
        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #eee;
            display: flex;
            justify-content: space-around;
            padding: 10px 0;
        }

        .nav-item {
            text-align: center;
            color: #666;
            font-size: 12px;
            cursor: pointer;
            transition: color 0.3s;
        }

        .nav-item.active {
            color: #667eea;
        }

        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }

        /* 日记页面 */
        .diary-editor {
            padding: 20px;
        }

        .diary-date {
            text-align: center;
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
        }

        .diary-textarea {
            width: 100%;
            height: 300px;
            border: 2px solid #eee;
            border-radius: 12px;
            padding: 15px;
            font-size: 14px;
            resize: none;
            font-family: inherit;
        }

        .diary-textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        /* 计划页面 */
        .plan-item {
            background: white;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .plan-checkbox {
            width: 20px;
            height: 20px;
            border: 2px solid #ddd;
            border-radius: 4px;
            margin-right: 15px;
            cursor: pointer;
        }

        .plan-checkbox.checked {
            background: #667eea;
            border-color: #667eea;
        }

        .plan-text {
            flex: 1;
            font-size: 14px;
            color: #333;
        }

        /* 财务页面 */
        .balance-card {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 25px;
            border-radius: 16px;
            text-align: center;
            margin-bottom: 20px;
        }

        .balance-amount {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .transaction-item {
            background: white;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .transaction-desc {
            font-size: 14px;
            color: #333;
        }

        .transaction-amount {
            font-weight: 600;
        }

        .amount-negative {
            color: #ff6b6b;
        }

        .amount-positive {
            color: #51cf66;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .ui-grid {
                grid-template-columns: 1fr;
            }

            h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 监督App UI设计稿展示</h1>

        <div class="ui-grid">
            <!-- 登录页面 -->
            <div class="phone-mockup">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>📶 📶 🔋</span>
                    </div>
                    <div class="content">
                        <div class="login-form">
                            <div class="logo">监督</div>
                            <h2 style="margin-bottom: 30px; color: #333;">欢迎回来</h2>
                            <div class="input-group">
                                <input type="text" class="input-field" placeholder="手机号/邮箱">
                            </div>
                            <div class="input-group">
                                <input type="password" class="input-field" placeholder="密码">
                            </div>
                            <button class="btn-primary">登录</button>
                            <p style="margin-top: 20px; color: #666; font-size: 14px;">
                                还没有账号？<a href="#" style="color: #667eea;">立即注册</a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 角色选择页面 -->
            <div class="phone-mockup">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>📶 📶 🔋</span>
                    </div>
                    <div class="content">
                        <div class="role-selection">
                            <h2 style="margin-bottom: 30px; color: #333;">选择您的角色</h2>
                            <div class="role-card">
                                <div class="role-icon">👥</div>
                                <h3 style="margin-bottom: 10px; color: #333;">监督人</h3>
                                <p style="color: #666; font-size: 14px;">帮助他人养成好习惯<br>制定计划和目标</p>
                            </div>
                            <div class="role-card">
                                <div class="role-icon">🎯</div>
                                <h3 style="margin-bottom: 10px; color: #333;">被监督人</h3>
                                <p style="color: #666; font-size: 14px;">接受监督和指导<br>完成目标和计划</p>
                            </div>
                            <button class="btn-primary" style="margin-top: 30px;">确认选择</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 被监督人主页 -->
            <div class="phone-mockup">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>📶 📶 🔋</span>
                    </div>
                    <div class="content">
                        <div class="dashboard">
                            <div class="welcome-card">
                                <h3>早上好，小明 👋</h3>
                                <p style="margin-top: 8px; opacity: 0.9;">今天也要加油哦！</p>
                            </div>

                            <div class="stats-grid">
                                <div class="stat-card">
                                    <div class="stat-number">7</div>
                                    <div class="stat-label">连续打卡天数</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">85%</div>
                                    <div class="stat-label">本月完成率</div>
                                </div>
                            </div>

                            <h4 style="margin-bottom: 15px; color: #333;">今日任务</h4>
                            <div class="task-card">
                                <div class="task-title">写今日日记</div>
                                <div class="task-time">截止时间：今天 23:59</div>
                                <span class="task-status status-pending">待完成</span>
                            </div>

                            <div class="task-card">
                                <div class="task-title">晨跑30分钟</div>
                                <div class="task-time">计划时间：07:00-07:30</div>
                                <span class="task-status status-approved">已完成</span>
                            </div>
                        </div>
                    </div>
                    <div class="bottom-nav">
                        <div class="nav-item active">
                            <div class="nav-icon">🏠</div>
                            <div>首页</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon">📝</div>
                            <div>日记</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon">📋</div>
                            <div>计划</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon">💰</div>
                            <div>财务</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon">👤</div>
                            <div>我的</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 日记页面 -->
            <div class="phone-mockup">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>📶 📶 🔋</span>
                    </div>
                    <div class="page-title">📝 今日日记</div>
                    <div class="content">
                        <div class="diary-editor">
                            <div class="diary-date">2024年1月15日 星期一</div>
                            <textarea class="diary-textarea" placeholder="记录今天的收获和感悟...

今天完成了什么？
遇到了什么困难？
有什么新的想法？
明天想要改进什么？"></textarea>
                            <button class="btn-primary" style="margin-top: 20px;">提交日记</button>
                        </div>
                    </div>
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div class="nav-icon">🏠</div>
                            <div>首页</div>
                        </div>
                        <div class="nav-item active">
                            <div class="nav-icon">📝</div>
                            <div>日记</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon">📋</div>
                            <div>计划</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon">💰</div>
                            <div>财务</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon">👤</div>
                            <div>我的</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 计划页面 -->
            <div class="phone-mockup">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>📶 📶 🔋</span>
                    </div>
                    <div class="page-title">📋 我的计划</div>
                    <div class="content">
                        <div style="padding: 20px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                                <h4 style="color: #333;">今日计划</h4>
                                <button style="background: #667eea; color: white; border: none; padding: 8px 16px; border-radius: 20px; font-size: 12px;">+ 添加</button>
                            </div>

                            <div class="plan-item">
                                <div class="plan-checkbox checked"></div>
                                <div class="plan-text">晨跑30分钟</div>
                                <span style="color: #51cf66; font-size: 12px;">✓</span>
                            </div>

                            <div class="plan-item">
                                <div class="plan-checkbox"></div>
                                <div class="plan-text">阅读30页书</div>
                            </div>

                            <div class="plan-item">
                                <div class="plan-checkbox"></div>
                                <div class="plan-text">写今日日记</div>
                            </div>

                            <h4 style="color: #333; margin: 30px 0 15px;">监督人安排的任务</h4>

                            <div class="task-card">
                                <div class="task-title">学习新技能</div>
                                <div class="task-time">监督人：张老师</div>
                                <p style="font-size: 12px; color: #666; margin: 8px 0;">每天至少学习1小时编程</p>
                                <span class="task-status status-pending">进行中</span>
                            </div>

                            <div class="task-card">
                                <div class="task-title">健康生活</div>
                                <div class="task-time">监督人：张老师</div>
                                <p style="font-size: 12px; color: #666; margin: 8px 0;">每天运动不少于30分钟</p>
                                <span class="task-status status-approved">已完成</span>
                            </div>
                        </div>
                    </div>
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div class="nav-icon">🏠</div>
                            <div>首页</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon">📝</div>
                            <div>日记</div>
                        </div>
                        <div class="nav-item active">
                            <div class="nav-icon">📋</div>
                            <div>计划</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon">💰</div>
                            <div>财务</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon">👤</div>
                            <div>我的</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 财务页面 -->
            <div class="phone-mockup">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>📶 📶 🔋</span>
                    </div>
                    <div class="page-title">💰 财务管理</div>
                    <div class="content">
                        <div style="padding: 20px;">
                            <div class="balance-card">
                                <div style="font-size: 14px; opacity: 0.9; margin-bottom: 10px;">当前余额</div>
                                <div class="balance-amount">¥ 850.00</div>
                                <div style="font-size: 12px; opacity: 0.8;">本月扣款：¥150.00</div>
                            </div>

                            <h4 style="color: #333; margin-bottom: 15px;">交易记录</h4>

                            <div class="transaction-item">
                                <div>
                                    <div class="transaction-desc">未写日记扣款</div>
                                    <div style="font-size: 12px; color: #666;">2024-01-14 23:59</div>
                                </div>
                                <div class="transaction-amount amount-negative">-¥20.00</div>
                            </div>

                            <div class="transaction-item">
                                <div>
                                    <div class="transaction-desc">完成运动计划奖励</div>
                                    <div style="font-size: 12px; color: #666;">2024-01-14 08:30</div>
                                </div>
                                <div class="transaction-amount amount-positive">+¥10.00</div>
                            </div>

                            <div class="transaction-item">
                                <div>
                                    <div class="transaction-desc">连续打卡7天奖励</div>
                                    <div style="font-size: 12px; color: #666;">2024-01-13 20:00</div>
                                </div>
                                <div class="transaction-amount amount-positive">+¥50.00</div>
                            </div>

                            <div class="transaction-item">
                                <div>
                                    <div class="transaction-desc">未完成学习任务</div>
                                    <div style="font-size: 12px; color: #666;">2024-01-12 23:59</div>
                                </div>
                                <div class="transaction-amount amount-negative">-¥30.00</div>
                            </div>
                        </div>
                    </div>
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div class="nav-icon">🏠</div>
                            <div>首页</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon">📝</div>
                            <div>日记</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon">📋</div>
                            <div>计划</div>
                        </div>
                        <div class="nav-item active">
                            <div class="nav-icon">💰</div>
                            <div>财务</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon">👤</div>
                            <div>我的</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 监督人主页 -->
            <div class="phone-mockup">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>📶 📶 🔋</span>
                    </div>
                    <div class="content">
                        <div class="dashboard">
                            <div class="welcome-card">
                                <h3>监督人面板 👨‍🏫</h3>
                                <p style="margin-top: 8px; opacity: 0.9;">管理您的监督对象</p>
                            </div>

                            <div class="stats-grid">
                                <div class="stat-card">
                                    <div class="stat-number">3</div>
                                    <div class="stat-label">监督对象</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">12</div>
                                    <div class="stat-label">待审核事项</div>
                                </div>
                            </div>

                            <h4 style="margin-bottom: 15px; color: #333;">待处理申请</h4>
                            <div class="task-card">
                                <div class="task-title">小明申请：周末看电影</div>
                                <div class="task-time">申请时间：2小时前</div>
                                <p style="font-size: 12px; color: #666; margin: 8px 0;">理由：这周学习任务都完成了，想放松一下</p>
                                <div style="display: flex; gap: 10px; margin-top: 15px;">
                                    <button style="background: #51cf66; color: white; border: none; padding: 8px 20px; border-radius: 20px; font-size: 12px;">同意</button>
                                    <button style="background: #ff6b6b; color: white; border: none; padding: 8px 20px; border-radius: 20px; font-size: 12px;">拒绝</button>
                                </div>
                            </div>

                            <div class="task-card">
                                <div class="task-title">小红申请：购买新书</div>
                                <div class="task-time">申请时间：5小时前</div>
                                <p style="font-size: 12px; color: #666; margin: 8px 0;">想买一本关于心理学的书籍</p>
                                <span class="task-status status-approved">已同意</span>
                            </div>
                        </div>
                    </div>
                    <div class="bottom-nav">
                        <div class="nav-item active">
                            <div class="nav-icon">🏠</div>
                            <div>首页</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon">👥</div>
                            <div>监督</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon">📊</div>
                            <div>统计</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon">⚙️</div>
                            <div>设置</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 申请页面 -->
            <div class="phone-mockup">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>📶 📶 🔋</span>
                    </div>
                    <div class="page-title">📝 提交申请</div>
                    <div class="content">
                        <div style="padding: 20px;">
                            <div class="input-group">
                                <label style="display: block; margin-bottom: 8px; color: #333; font-weight: 500;">申请事项</label>
                                <input type="text" class="input-field" placeholder="请输入您想要做的事情">
                            </div>

                            <div class="input-group">
                                <label style="display: block; margin-bottom: 8px; color: #333; font-weight: 500;">申请理由</label>
                                <textarea class="input-field" style="height: 120px; resize: none;" placeholder="请详细说明申请理由..."></textarea>
                            </div>

                            <div class="input-group">
                                <label style="display: block; margin-bottom: 8px; color: #333; font-weight: 500;">预计时间</label>
                                <input type="datetime-local" class="input-field">
                            </div>

                            <div class="input-group">
                                <label style="display: block; margin-bottom: 8px; color: #333; font-weight: 500;">优先级</label>
                                <select class="input-field">
                                    <option>普通</option>
                                    <option>重要</option>
                                    <option>紧急</option>
                                </select>
                            </div>

                            <button class="btn-primary" style="margin-top: 20px;">提交申请</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 统计页面 -->
            <div class="phone-mockup">
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>📶 📶 🔋</span>
                    </div>
                    <div class="page-title">📊 数据统计</div>
                    <div class="content">
                        <div style="padding: 20px;">
                            <div style="background: white; border-radius: 16px; padding: 20px; margin-bottom: 20px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                                <h4 style="color: #333; margin-bottom: 15px;">本月完成情况</h4>
                                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                    <span style="color: #666;">日记完成率</span>
                                    <span style="color: #667eea; font-weight: 600;">28/31 (90%)</span>
                                </div>
                                <div style="background: #f0f0f0; height: 8px; border-radius: 4px; margin-bottom: 15px;">
                                    <div style="background: linear-gradient(90deg, #667eea, #764ba2); width: 90%; height: 100%; border-radius: 4px;"></div>
                                </div>

                                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                    <span style="color: #666;">计划完成率</span>
                                    <span style="color: #667eea; font-weight: 600;">85%</span>
                                </div>
                                <div style="background: #f0f0f0; height: 8px; border-radius: 4px;">
                                    <div style="background: linear-gradient(90deg, #51cf66, #40c057); width: 85%; height: 100%; border-radius: 4px;"></div>
                                </div>
                            </div>

                            <div style="background: white; border-radius: 16px; padding: 20px; margin-bottom: 20px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                                <h4 style="color: #333; margin-bottom: 15px;">习惯养成</h4>
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                    <div>
                                        <div style="font-weight: 600; color: #333;">早起</div>
                                        <div style="font-size: 12px; color: #666;">连续7天</div>
                                    </div>
                                    <div style="color: #51cf66; font-size: 24px;">🔥</div>
                                </div>

                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                    <div>
                                        <div style="font-weight: 600; color: #333;">运动</div>
                                        <div style="font-size: 12px; color: #666;">连续12天</div>
                                    </div>
                                    <div style="color: #51cf66; font-size: 24px;">🔥</div>
                                </div>

                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <div style="font-weight: 600; color: #333;">阅读</div>
                                        <div style="font-size: 12px; color: #666;">连续3天</div>
                                    </div>
                                    <div style="color: #ffa502; font-size: 24px;">⭐</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div class="nav-icon">🏠</div>
                            <div>首页</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon">📝</div>
                            <div>日记</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon">📋</div>
                            <div>计划</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav-icon">💰</div>
                            <div>财务</div>
                        </div>
                        <div class="nav-item active">
                            <div class="nav-icon">👤</div>
                            <div>我的</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能说明 -->
        <div style="background: white; border-radius: 20px; padding: 30px; margin-top: 40px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
            <h2 style="color: #333; margin-bottom: 20px; text-align: center;">📋 功能特色说明</h2>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <div style="padding: 20px; border-left: 4px solid #667eea;">
                    <h3 style="color: #667eea; margin-bottom: 10px;">🎯 双角色系统</h3>
                    <p style="color: #666; line-height: 1.6;">监督人和被监督人两种角色，清晰的权限分工，监督人可以审核申请、发布任务，被监督人专注于完成目标。</p>
                </div>

                <div style="padding: 20px; border-left: 4px solid #51cf66;">
                    <h3 style="color: #51cf66; margin-bottom: 10px;">📝 智能日记系统</h3>
                    <p style="color: #666; line-height: 1.6;">每日日记提醒，未提交自动扣款机制，帮助养成反思和记录的好习惯。</p>
                </div>

                <div style="padding: 20px; border-left: 4px solid #ff6b6b;">
                    <h3 style="color: #ff6b6b; margin-bottom: 10px;">💰 激励财务系统</h3>
                    <p style="color: #666; line-height: 1.6;">完成任务获得奖励，未完成扣款，通过经济激励促进目标达成。</p>
                </div>

                <div style="padding: 20px; border-left: 4px solid #ffa502;">
                    <h3 style="color: #ffa502; margin-bottom: 10px;">📊 数据可视化</h3>
                    <p style="color: #666; line-height: 1.6;">详细的完成率统计、习惯养成追踪，让进步看得见。</p>
                </div>

                <div style="padding: 20px; border-left: 4px solid #9c88ff;">
                    <h3 style="color: #9c88ff; margin-bottom: 10px;">🔄 申请审核流程</h3>
                    <p style="color: #666; line-height: 1.6;">被监督人需要申请才能做某些事情，监督人审核同意后才能执行，增强自控力。</p>
                </div>

                <div style="padding: 20px; border-left: 4px solid #2ed573;">
                    <h3 style="color: #2ed573; margin-bottom: 10px;">📱 移动端优先</h3>
                    <p style="color: #666; line-height: 1.6;">专为手机设计的界面，随时随地记录和管理，操作简单直观。</p>
                </div>
            </div>
        </div>

        <!-- 技术架构说明 -->
        <div style="background: white; border-radius: 20px; padding: 30px; margin-top: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
            <h2 style="color: #333; margin-bottom: 20px; text-align: center;">🛠️ 技术架构建议</h2>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                <div style="text-align: center; padding: 20px;">
                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px; font-size: 24px;">📱</div>
                    <h4 style="color: #333; margin-bottom: 10px;">前端技术</h4>
                    <p style="color: #666; font-size: 14px;">React Native / Flutter<br>支持iOS和Android双平台</p>
                </div>

                <div style="text-align: center; padding: 20px;">
                    <div style="background: linear-gradient(135deg, #51cf66 0%, #40c057 100%); color: white; width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px; font-size: 24px;">⚙️</div>
                    <h4 style="color: #333; margin-bottom: 10px;">后端技术</h4>
                    <p style="color: #666; font-size: 14px;">Node.js + Express<br>或 Spring Boot</p>
                </div>

                <div style="text-align: center; padding: 20px;">
                    <div style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); color: white; width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px; font-size: 24px;">🗄️</div>
                    <h4 style="color: #333; margin-bottom: 10px;">数据库</h4>
                    <p style="color: #666; font-size: 14px;">MySQL / PostgreSQL<br>Redis缓存</p>
                </div>

                <div style="text-align: center; padding: 20px;">
                    <div style="background: linear-gradient(135deg, #ffa502 0%, #ff6348 100%); color: white; width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px; font-size: 24px;">🔔</div>
                    <h4 style="color: #333; margin-bottom: 10px;">推送服务</h4>
                    <p style="color: #666; font-size: 14px;">Firebase / 极光推送<br>定时提醒功能</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>